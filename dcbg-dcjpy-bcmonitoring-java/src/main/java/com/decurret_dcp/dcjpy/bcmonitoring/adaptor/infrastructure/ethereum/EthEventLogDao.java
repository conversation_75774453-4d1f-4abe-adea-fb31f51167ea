package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import static com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter.TUPLE_ARRAY_TYPE;
import static com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter.TUPLE_TYPE;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.reactivex.disposables.Disposable;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventValues;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.StaticStruct;
import org.web3j.abi.datatypes.Type;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.DefaultBlockParameterNumber;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.tx.Contract;

@Component
public class EthEventLogDao {
  private final LoggingService logger;
  private final BcmonitoringConfigurationProperties properties;
  private final Web3jConfig web3jConfig;
  private final AbiParser abiParser;
  private final ObjectMapper objectMapper;
  private Disposable subscription;

  /**
   * Constructor for EthEventLogDao.
   *
   * @param log The logging service.
   * @param properties The configuration properties.
   * @param web3jConfig The Web3j configuration.
   * @param abiParser The ABI parser.
   * @param objectMapper The object mapper.
   */
  public EthEventLogDao(
      LoggingService log,
      BcmonitoringConfigurationProperties properties,
      Web3jConfig web3jConfig,
      AbiParser abiParser,
      ObjectMapper objectMapper) {
    this.logger = log;
    this.properties = properties;
    this.web3jConfig = web3jConfig;
    this.abiParser = abiParser;
    this.objectMapper = objectMapper;
  }

  /**
   * Subscribes to all blocks and processes transactions.
   *
   * @return A BlockingQueue of Transaction objects.
   */
  public BlockingQueue<Transaction> subscribeAll() {
    BlockingQueue<Transaction> transactions = new LinkedBlockingQueue<>(Integer.MAX_VALUE);

    // Check if the difference is valid
    int allowableDiff;
    try {
      allowableDiff =
          Integer.parseInt(properties.getSubscription().getAllowableBlockTimestampDiffSec());
    } catch (NumberFormatException e) {
      logger.error("Failed to parse allowable timestamp difference", e);
      return null;
    }

    try {
      // Create a new Web3j instance for this subscription
      Web3j web3j = web3jConfig.getWeb3j();
      // Create a new Web3j instance for RPC API calls
      Web3j web3jCaller = web3jConfig.getWeb3jCaller();
      // Subscribe to new blocks
      this.subscription =
          web3j
              .newHeadsNotifications()
              .subscribe(
                  newHeadsNotification -> {
                    try {
                      EthBlock.Block block =
                          web3jCaller
                              .ethGetBlockByNumber(
                                  () -> newHeadsNotification.getParams().getResult().getNumber(),
                                  true)
                              .send()
                              .getBlock();

                      BigInteger blockNumber = block.getNumber();
                      if (isDelayed(block, allowableDiff)) {
                        logger.warn(
                            "Block {} is delayed by more than {} seconds",
                            blockNumber,
                            allowableDiff);
                      }

                      // Check if block has no transactions
                      if (block.getTransactions() == null || block.getTransactions().isEmpty()) {
                        logger.info("Block {} has no transactions", blockNumber);
                        return;
                      }

                      // Process block transactions and events
                      List<Event> events = null;
                      try {
                        events = convBlock2EventEntities(block);
                      } catch (Exception e) {
                        throw new RuntimeException(e);
                      }

                      // Always create transaction, even if events list is empty
                      if (events != null) {
                        if (!events.isEmpty()) {
                          logger.info("detect block includes events");
                        }

                        BlockHeight blockHeight =
                            BlockHeight.builder().blockNumber(blockNumber.longValue()).build();
                        Transaction transaction =
                            Transaction.builder().events(events).blockHeight(blockHeight).build();
                        try {
                          transactions.put(transaction);
                        } catch (InterruptedException e) {
                          logger.error("Failed to put transaction", e);
                          throw new RuntimeException(e);
                        }
                      }
                    } catch (Exception throwable) {
                      logger.error("Error processing block", throwable);
                      // Put error transaction to signal error condition
                      try {
                        transactions.put(
                            Transaction.builder()
                                .blockHeight(BlockHeight.builder().blockNumber(-1).build())
                                .build());
                      } catch (InterruptedException e) {
                        logger.error("Failed to put error transaction", e);
                        Thread.currentThread().interrupt();
                      }
                    }
                  },
                  error -> {
                    logger.error("Subscription error", error);
                    unsubscribe();
                    web3jConfig.shutdownWeb3j();
                    transactions.put(
                        Transaction.builder()
                            .blockHeight(BlockHeight.builder().blockNumber(-1).build())
                            .build());
                  },
                  () -> logger.info("Subscription completed"));
      return transactions;
    } catch (Exception e) {
      logger.error("Failed to create Web3j subscription", e);
      throw e;
    }
  }

  /**
   * Checks if the block is delayed based on the allowable difference in seconds.
   *
   * @param block The block to check.
   * @param allowableDiffSeconds The allowable difference in seconds.
   * @return true if the block is delayed, false otherwise.
   */
  private boolean isDelayed(EthBlock.Block block, int allowableDiffSeconds) {
    long blockTimestamp = block.getTimestamp().longValue();
    long currentTime = Instant.now().getEpochSecond();
    long diff = currentTime - blockTimestamp;

    return diff > allowableDiffSeconds;
  }

  /**
   * Converts a block to a collection of event entities.
   *
   * @param block Ethereum block
   * @return Collection of events found in the block
   * @throws IOException If there is an error communicating with the Ethereum node
   * @throws ExecutionException If there is an error executing the transaction
   * @throws InterruptedException If the operation is interrupted
   */
  public List<Event> convBlock2EventEntities(EthBlock.Block block) throws Exception {
    List<Event> events = new ArrayList<>();

    try {
      // Create a new Web3j instance for RPC API calls
      Web3j web3jCaller = web3jConfig.getWeb3jCaller();

      for (EthBlock.TransactionResult txResult : block.getTransactions()) {
        // Return error if transaction is null
        if (txResult.get() == null) {
          throw new RuntimeException("Transaction is null");
        }

        try {
          EthBlock.TransactionObject transaction = (EthBlock.TransactionObject) txResult.get();
          String transactionHash = transaction.getHash();

          EthGetTransactionReceipt receiptResponse =
              web3jCaller.ethGetTransactionReceipt(transactionHash).send();

          TransactionReceipt receipt = receiptResponse.getTransactionReceipt().orElse(null);
          if (receipt == null) {
            throw new RuntimeException("Transaction receipt is null");
          }

          for (Log log : receipt.getLogs()) {
            try {
              logger.info("Event found tx_hash={}", log.getTransactionHash());
              Event event =
                  convertEthLogToEventEntity(log)
                      .withBlockTimestamp(block.getTimestamp().longValue());
              logger.info("Event parsed tx_hash={}, name={}", event.transactionHash, event.name);
              events.add(event);
            } catch (Exception e) {
              logger.error("Error processing log for transaction {}", log.getTransactionHash());
              throw e;
            }
          }
        } catch (Exception e) {
          logger.error("Error processing transaction", e.getMessage());
          throw e;
        }
      }
    } catch (Exception e) {
      logger.error("Error converting block to events: {}", e.getMessage());
      throw e;
    }

    return events;
  }

  /**
   * Converts an Ethereum log to an Event entity.
   *
   * @param ethLog The Ethereum log to convert
   * @return Converted Event entity
   * @throws Exception If conversion fails
   */
  public Event convertEthLogToEventEntity(Log ethLog) throws Exception {
    try {
      // Get ABI event definition for the log
      org.web3j.abi.datatypes.Event abiEvent = abiParser.getABIEventByLog(ethLog);
      if (abiEvent == null) {
        logger.info("Event definition not found in ABI");
        throw new Exception("Event definition not found in ABI");
      }

      // Get contract ABI event to access parameter names
      var contractAbiEvent = abiParser.getContractAbiEventByLog(ethLog);

      // Extract event parameters using Web3j's utilities
      EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);
      if (eventValues == null) {
        logger.info("No event values found for log: {}", ethLog);
        throw new Exception("No event values found for log");
      }

      List<Type> indexedParameters = eventValues.getIndexedValues();
      List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();
      Map<Boolean, List<AbiParser.AbiEventInput>> groupedInputs =
          contractAbiEvent.getInputs().stream()
              .collect(
                  Collectors.groupingBy(
                      AbiParser.AbiEventInput::isIndexed, LinkedHashMap::new, Collectors.toList()));

      Map<String, Object> indexedValues =
          decodeEventParameters(
              indexedParameters, groupedInputs.getOrDefault(Boolean.TRUE, Collections.emptyList()));
      Map<String, Object> nonIndexedValues =
          decodeEventParameters(
              nonIndexedParameters,
              groupedInputs.getOrDefault(Boolean.FALSE, Collections.emptyList()),
              ethLog.getData());

      String indexedJson = objectMapper.writeValueAsString(indexedValues);
      String nonIndexedJson = objectMapper.writeValueAsString(nonIndexedValues);

      // Serialize log to JSON
      String logJson = objectMapper.writeValueAsString(ethLog);

      // Create and return new Event entity
      return Event.builder()
          .name(abiEvent.getName())
          .transactionHash(ethLog.getTransactionHash())
          .logIndex((int) ethLog.getLogIndex().longValue())
          .indexedValues(indexedJson)
          .nonIndexedValues(nonIndexedJson)
          .log(logJson)
          .build();
    } catch (Exception e) {
      logger.error("Error converting log to event entity", e);
      return null;
    }
  }

  /**
   * Decodes a list of event parameter values based on their corresponding ABI event input
   * definitions.
   *
   * <p>This method maps the decoded values to their respective parameter names as defined in the
   * ABI, supporting complex types such as tuples, tuple arrays, and dynamic arrays.
   *
   * @param values the list of decoded {@link Type} values from the event log
   * @param inputs the list of expected ABI event input definitions
   * @return a map of parameter names to their decoded values, preserving the order of inputs
   */
  private Map<String, Object> decodeEventParameters(
      List<Type> values, List<AbiParser.AbiEventInput> inputs) {
    return decodeEventParameters(values, inputs, null);
  }

  private Map<String, Object> decodeEventParameters(
      List<Type> values, List<AbiParser.AbiEventInput> inputs, String rawHexData) {
    Map<String, Object> result = new LinkedHashMap<>();
    int index = 0;
    for (var input : inputs) {
      if (index >= values.size()) break;

      String name = input.getName();
      Object value = values.get(index).getValue();

      if (TUPLE_TYPE.equals(input.getType()) && value instanceof List<?> list) {
        // Handle tuple type: tuple
        result.put(name, decodeTuple(list, input.getComponents()));
      } else if (TUPLE_ARRAY_TYPE.equals(input.getType()) && value instanceof List<?> list) {
        // Handle tuple array type: tuple[]
        logger.debug("Processing tuple array field: " + name + " with " + list.size() + " elements");
        logger.debug("Raw list contents: " + list);

        // Check if this is the Web3j tuple array bug where all elements have the same values
        List<Map<String, Object>> decodedArray = decodeTupleArray(list, input.getComponents());
        if (decodedArray.size() > 1 && hasWeb3jTupleArrayBug(decodedArray)) {
          logger.debug("Detected Web3j tuple array bug - attempting manual decode from raw data");
          // Try to manually decode from raw hex data
          List<Map<String, Object>> manuallyDecodedArray = manuallyDecodeTupleArray(name, input.getComponents(), decodedArray.size(), rawHexData);
          if (manuallyDecodedArray != null) {
            logger.debug("Successfully manually decoded tuple array");
            result.put(name, manuallyDecodedArray);
          } else {
            logger.debug("Manual decoding failed, using Web3j result with bug");
            result.put(name, decodedArray);
          }
        } else {
          result.put(name, decodedArray);
        }
      } else if (input.getType() != null
          && input.getType().endsWith("[]")
          && values.get(index) instanceof DynamicArray<?>) {
        // Handle dynamic array type: uint8[], bytes32[], etc.
        result.put(name, decodeDynamicArray(value));
      } else {
        // Handle basic types: unit8, bytes32, etc.
        result.put(name, value);
      }

      index++;
    }
    return result;
  }

  /**
   * Decode Tuple
   *
   * @param list a list of Type objects
   * @param components a list of AbiEventInput objects
   * @return a map of strings to objects
   */
  private Map<String, Object> decodeTuple(List<?> list, List<AbiParser.AbiEventInput> components) {
    Map<String, Object> tupleResult = new LinkedHashMap<>();
    for (int i = 0; i < components.size(); i++) {
      String compName = components.get(i).getName();
      Object compValue = ((Type<?>) list.get(i)).getValue();

      if (TUPLE_TYPE.equals(components.get(i).getType())
          && compValue instanceof List<?> nestedList) {
        tupleResult.put(compName, decodeTuple(nestedList, components.get(i).getComponents()));
      } else {
        if (list.get(i) instanceof DynamicArray<?>) {
          tupleResult.put(compName, decodeDynamicArray(compValue));
        } else {
          tupleResult.put(compName, compValue);
        }
      }
    }
    return tupleResult;
  }

  /**
   * Decode Tuple Array
   *
   * @param list a list of Type objects representing an array of tuples
   * @param components a list of AbiEventInput objects defining the tuple structure
   * @return a list of maps, each representing a decoded tuple
   */
  private List<Map<String, Object>> decodeTupleArray(
      List<?> list, List<AbiParser.AbiEventInput> components) {
    List<Map<String, Object>> tupleArrayResult = new ArrayList<>();

    logger.debug("Decoding tuple array with " + list.size() + " elements");

    // WORKAROUND: Web3j has a bug where it creates duplicate references to the same struct object
    // instead of creating separate struct instances for each element in a tuple array.
    // We need to handle this case by checking if all elements are the same object reference.

    if (list.size() > 1) {
      // Check if all elements are the same object reference (Web3j bug)
      boolean allSameReference = true;
      Object firstElement = list.get(0);
      logger.debug("First element reference: " + System.identityHashCode(firstElement));
      for (int i = 1; i < list.size(); i++) {
        Object currentElement = list.get(i);
        logger.debug("Element " + i + " reference: " + System.identityHashCode(currentElement));
        if (currentElement != firstElement) { // Using != for reference inequality
          logger.debug("Elements are different references");
          allSameReference = false;
          break;
        } else {
          logger.debug("Elements are the same reference");
        }
      }

      logger.debug("All same reference: " + allSameReference);
      if (allSameReference) {
        logger.debug("Detected Web3j tuple array bug - all elements are the same object reference");
        // This is the Web3j bug case - we need to decode manually from the original struct
        // For now, return an error message to indicate this needs manual handling
        throw new RuntimeException("Web3j tuple array decoding bug detected. This requires manual ABI decoding from raw data.");
      }
    }

    // Normal case - each element is a separate struct object
    for (int i = 0; i < list.size(); i++) {
      Object element = list.get(i);
      logger.debug("Processing tuple array element " + i + ": " + element.getClass().getSimpleName());

      if (element instanceof DynamicStruct struct) {
        // Extract the components from the struct and decode them
        List<Type> structValues = struct.getValue();
        logger.debug("DynamicStruct has " + structValues.size() + " components");
        logger.debug("  StructValues list reference: " + System.identityHashCode(structValues));
        for (int j = 0; j < structValues.size(); j++) {
          Type component = structValues.get(j);
          logger.debug("  Component " + j + ": " + component.getClass().getSimpleName() + " = " + component.getValue() + " (ref: " + System.identityHashCode(component) + ")");
        }
        Map<String, Object> decodedTuple = decodeTuple(structValues, components);
        tupleArrayResult.add(decodedTuple);
      } else if (element instanceof StaticStruct struct) {
        // Extract the components from the struct and decode them
        List<Type> structValues = struct.getValue();
        logger.debug("StaticStruct has " + structValues.size() + " components");
        logger.debug("  StructValues list reference: " + System.identityHashCode(structValues));
        for (int j = 0; j < structValues.size(); j++) {
          Type component = structValues.get(j);
          logger.debug("  Component " + j + ": " + component.getClass().getSimpleName() + " = " + component.getValue() + " (ref: " + System.identityHashCode(component) + ")");
        }
        Map<String, Object> decodedTuple = decodeTuple(structValues, components);
        tupleArrayResult.add(decodedTuple);
      }
    }

    logger.debug("Decoded tuple array result: " + tupleArrayResult);
    return tupleArrayResult;
  }

  /**
   * Check if the decoded tuple array has the Web3j bug where all elements have identical values
   *
   * @param decodedArray the decoded tuple array
   * @return true if all elements have identical values (indicating the Web3j bug)
   */
  private boolean hasWeb3jTupleArrayBug(List<Map<String, Object>> decodedArray) {
    if (decodedArray.size() <= 1) {
      return false;
    }

    Map<String, Object> firstElement = decodedArray.get(0);
    for (int i = 1; i < decodedArray.size(); i++) {
      Map<String, Object> currentElement = decodedArray.get(i);
      if (!firstElement.equals(currentElement)) {
        return false; // Elements are different, no bug
      }
    }

    logger.debug("All tuple array elements have identical values - Web3j bug detected");
    return true; // All elements are identical, likely the Web3j bug
  }

  /**
   * Decode Dynamic Array
   *
   * @param value an ArrayList of Type objects
   * @return a list of objects
   */
  private List<Object> decodeDynamicArray(Object value) {
    ArrayList<Type> dynamicArray = (ArrayList<Type>) value;
    return dynamicArray.stream().map(Type::getValue).toList();
  }

  /**
   * Get component value from list instance of DynamicStruct or StaticStruct
   *
   * @param list a list of Type objects
   * @param i index of the list
   * @return value of the component
   * @throws RuntimeException if list is empty
   */
  private static Object getComponentValue(List<?> list, int i) {
    List<Type> values = List.of();

    // First item is the root struct
    if (list.getFirst() instanceof DynamicStruct struct) {
      values = struct.getValue();
    }
    if (list.getFirst() instanceof StaticStruct struct) {
      values = struct.getValue();
    }
    if (values.isEmpty()) {
      throw new RuntimeException("Error decoding dynamic array");
    }

    return values.get(i).getValue();
  }

  /**
   * Retrieves the block timestamp for a given block number.
   *
   * @param blockNumber The block number to retrieve the timestamp for.
   * @return The block timestamp in seconds since epoch.
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private long getBlockTimestamp(BigInteger blockNumber) throws IOException {
    // Create a new Web3j instance for this operation
    Web3j web3j = web3jConfig.getWeb3j();

    try {
      return web3j
          .ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), false)
          .send()
          .getBlock()
          .getTimestamp()
          .longValue();
    } finally {
      // Shutdown the Web3j instance to free resources
      web3j.shutdown();
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return Queue of transactions containing events
   */
  public List<Transaction> getPendingTransactions(long blockHeight) {
    return getPendingTransactions(blockHeight, false);
  }

  /**
   * Get filtered logs from a specific block height with an option to force an error in the outer
   * catch block This method is primarily used for testing the error handling in the outer catch
   * block
   *
   * @param blockHeight Block number to start from
   * @param forceOuterError Whether to force an error in the outer catch block (for testing)
   * @return Queue of transactions containing events
   */
  public List<Transaction> getPendingTransactions(long blockHeight, boolean forceOuterError) {
    try {
      // Create a new Web3j instance for this operation
      Web3j web3j = web3jConfig.getWeb3j();

      // Create filter to get logs from the specified block height
      EthFilter filter =
          new EthFilter(
              DefaultBlockParameter.valueOf(BigInteger.valueOf(blockHeight)),
              DefaultBlockParameter.valueOf("latest"),
              Collections.emptyList());

      // Get logs synchronously
      List<EthLog.LogResult> filterLogs = web3j.ethGetLogs(filter).send().getLogs();

      logger.info(
          "Retrieved {} logs from block height {} to latest", filterLogs.size(), blockHeight);

      // Collect block numbers
      List<BigInteger> blockNumbers =
          filterLogs.stream().map(result -> (Log) result.get()).map(Log::getBlockNumber).toList();

      // Fetch timestamps per block
      Map<BigInteger, BigInteger> blockTimestamps = new HashMap<>();
      for (BigInteger blockNumber : blockNumbers) {
        EthBlock block =
            web3j.ethGetBlockByNumber(new DefaultBlockParameterNumber(blockNumber), false).send();
        blockTimestamps.put(blockNumber, block.getBlock().getTimestamp());
      }

      if (forceOuterError) {
        throw new RuntimeException("Forced error in outer catch block for testing");
      }

      return filterLogs.stream()
          .map(
              logResult -> {
                try {
                  Log ethLog = (Log) logResult.get();
                  logger.info("Event found tx_hash={}", ethLog.getTransactionHash());

                  Event event =
                      convertEthLogToEventEntity(ethLog)
                          .withBlockTimestamp(
                              blockTimestamps.get(ethLog.getBlockNumber()).longValue());
                  logger.info(
                      "Event parsed tx_hash={}, name={}", event.transactionHash, event.name);

                  BlockHeight height =
                      BlockHeight.builder()
                          .blockNumber(ethLog.getBlockNumber().longValue())
                          .build();

                  return Transaction.builder()
                      .events(Collections.singletonList(event))
                      .blockHeight(height)
                      .build();
                } catch (Exception e) {
                  logger.error("Error processing individual log", e);
                  return null;
                }
              })
          .filter(Objects::nonNull)
          .toList();

    } catch (Exception e) {
      logger.error("Error getting filtered logs", e);
      throw new RuntimeException("Error getting filtered logs", e);
    }
  }

  public void unsubscribe() {
    if (subscription != null) {
      subscription.dispose();
    }
  }
}
